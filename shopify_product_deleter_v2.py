#!/usr/bin/env python3
"""
Shopify Product Bulk Deleter v2.0
Production-ready script to delete all products from a Shopify store.
Improved error handling for 404 errors (already deleted products).

Author: Print Garage
Date: 2025-07-29
"""

import requests
import json
import time
import logging
from datetime import datetime
from typing import List, Dict, Optional
import sys

# Configuration
SHOPIFY_STORE_URL = "theprintgarage.myshopify.com"
ACCESS_TOKEN = "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"
API_VERSION = "2023-10"
BASE_URL = f"https://{SHOPIFY_STORE_URL}/admin/api/{API_VERSION}"

# Rate limiting configuration
RATE_LIMIT_DELAY = 0.5  # seconds between requests
MAX_RETRIES = 3

# Setup logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler(f'shopify_deletion_v2_{datetime.now().strftime("%Y%m%d_%H%M%S")}.log'),
        logging.StreamHandler(sys.stdout)
    ]
)
logger = logging.getLogger(__name__)


class ShopifyProductDeleter:
    """Handle Shopify product deletion operations."""
    
    def __init__(self, store_url: str, access_token: str):
        self.store_url = store_url
        self.access_token = access_token
        self.headers = {
            "X-Shopify-Access-Token": access_token,
            "Content-Type": "application/json"
        }
        self.deleted_products = []
        self.failed_deletions = []
        
    def make_request(self, method: str, endpoint: str, data: Optional[Dict] = None) -> Optional[Dict]:
        """Make HTTP request to Shopify API with retry logic."""
        url = f"{BASE_URL}/{endpoint}"
        
        for attempt in range(MAX_RETRIES):
            try:
                if method.upper() == "GET":
                    response = requests.get(url, headers=self.headers, timeout=30)
                elif method.upper() == "DELETE":
                    response = requests.delete(url, headers=self.headers, timeout=30)
                else:
                    response = requests.request(method, url, headers=self.headers, json=data, timeout=30)
                
                # Handle rate limiting
                if response.status_code == 429:
                    retry_after = int(response.headers.get('Retry-After', 2))
                    logger.warning(f"Rate limited. Waiting {retry_after} seconds...")
                    time.sleep(retry_after)
                    continue
                
                # For DELETE requests, treat 404 as success (already deleted)
                if method.upper() == "DELETE" and response.status_code == 404:
                    logger.info(f"Product already deleted or not found (404) - treating as success")
                    time.sleep(RATE_LIMIT_DELAY)
                    return {"already_deleted": True}
                
                response.raise_for_status()
                
                # Add delay to respect rate limits
                time.sleep(RATE_LIMIT_DELAY)
                
                return response.json() if response.content else {}
                
            except requests.exceptions.RequestException as e:
                # For DELETE requests, check if it's a 404 error
                if method.upper() == "DELETE" and hasattr(e, 'response') and e.response.status_code == 404:
                    logger.info(f"Product already deleted or not found (404) - treating as success")
                    time.sleep(RATE_LIMIT_DELAY)
                    return {"already_deleted": True}
                
                logger.error(f"Request failed (attempt {attempt + 1}/{MAX_RETRIES}): {e}")
                if attempt == MAX_RETRIES - 1:
                    return None
                time.sleep(2 ** attempt)  # Exponential backoff
        
        return None
    
    def get_all_products(self) -> List[Dict]:
        """Fetch all products from the store."""
        logger.info("Fetching all products from the store...")
        all_products = []
        page_count = 0
        start_time = time.time()
        
        while True:
            page_count += 1
            endpoint = "products.json?limit=250"
            
            # Use since_id for pagination if we have products
            if all_products:
                last_product_id = all_products[-1]["id"]
                endpoint = f"products.json?limit=250&since_id={last_product_id}"
            
            page_start_time = time.time()
            response = self.make_request("GET", endpoint)
            if not response:
                logger.error("Failed to fetch products")
                break
            
            products = response.get("products", [])
            all_products.extend(products)
            
            # Calculate progress and ETA
            elapsed_time = time.time() - start_time
            avg_time_per_page = elapsed_time / page_count if page_count > 0 else 0
            
            if len(products) == 250:  # Full page, estimate more pages
                # Estimate total pages (rough estimate)
                estimated_total_pages = page_count + 5  # Conservative estimate
                remaining_pages = max(0, estimated_total_pages - page_count)
                eta_seconds = remaining_pages * avg_time_per_page
                eta_minutes = eta_seconds / 60
                
                logger.info(f"Fetched {len(all_products)} products (Page {page_count}) - "
                          f"ETA: ~{eta_minutes:.1f} minutes")
                print(f"📦 Progress: {len(all_products)} products fetched (Page {page_count}) - "
                      f"ETA: ~{eta_minutes:.1f} minutes")
                continue
            else:
                # Last page
                logger.info(f"Completed fetching. Total: {len(all_products)} products (Page {page_count})")
                print(f"✅ Fetch complete: {len(all_products)} products found")
                break
        
        logger.info(f"Found {len(all_products)} products to delete")
        return all_products
    
    def delete_product(self, product_id: int, product_title: str) -> bool:
        """Delete a single product."""
        logger.info(f"Deleting product: {product_title} (ID: {product_id})")
        
        response = self.make_request("DELETE", f"products/{product_id}.json")
        
        if response is not None:
            # Check if product was already deleted
            if response.get("already_deleted"):
                self.deleted_products.append({
                    "id": product_id,
                    "title": product_title,
                    "deleted_at": datetime.now().isoformat(),
                    "status": "already_deleted"
                })
                logger.info(f"Product was already deleted: {product_title}")
                print(f"   ℹ️  Already deleted: {product_title[:50]}...")
                return True
            else:
                self.deleted_products.append({
                    "id": product_id,
                    "title": product_title,
                    "deleted_at": datetime.now().isoformat(),
                    "status": "deleted"
                })
                logger.info(f"Successfully deleted: {product_title}")
                return True
        else:
            self.failed_deletions.append({
                "id": product_id,
                "title": product_title,
                "error": "API request failed"
            })
            logger.error(f"Failed to delete: {product_title}")
            print(f"   ❌ Failed: {product_title[:50]}...")
            return False

    def delete_all_products(self) -> Dict:
        """Delete all products and return summary."""
        logger.info("Starting bulk product deletion...")

        # Get all products
        print("🔍 Starting product discovery...")
        products = self.get_all_products()

        if not products:
            logger.warning("No products found to delete")
            print("ℹ️  No products found in the store.")
            return {"total": 0, "deleted": 0, "failed": 0}

        # Confirm deletion
        print(f"\n⚠️  WARNING: You are about to delete {len(products)} products from {SHOPIFY_STORE_URL}")
        print("This action cannot be undone!")
        print(f"📊 Total products to delete: {len(products)}")
        confirmation = input("\nType 'DELETE ALL PRODUCTS' to confirm: ")

        if confirmation != "DELETE ALL PRODUCTS":
            logger.info("Deletion cancelled by user")
            return {"total": len(products), "deleted": 0, "failed": 0}

        # Delete products
        deletion_start_time = time.time()
        for i, product in enumerate(products, 1):
            product_id = product["id"]
            product_title = product["title"]

            # Calculate progress and ETA
            if i > 1:  # Skip ETA calculation for first item
                elapsed_time = time.time() - deletion_start_time
                avg_time_per_deletion = elapsed_time / (i - 1)
                remaining_deletions = len(products) - i + 1
                eta_seconds = remaining_deletions * avg_time_per_deletion
                eta_minutes = eta_seconds / 60

                progress_percent = (i / len(products)) * 100
                logger.info(f"Progress: {i}/{len(products)} ({progress_percent:.1f}%) - "
                          f"ETA: {eta_minutes:.1f} min - Deleting: {product_title}")
                print(f"🗑️  Deleting {i}/{len(products)} ({progress_percent:.1f}%) - "
                      f"ETA: {eta_minutes:.1f} min - {product_title[:50]}...")
            else:
                logger.info(f"Progress: {i}/{len(products)} - Deleting: {product_title}")
                print(f"🗑️  Deleting {i}/{len(products)} - {product_title[:50]}...")

            self.delete_product(product_id, product_title)

        # Generate summary
        summary = {
            "total": len(products),
            "deleted": len(self.deleted_products),
            "failed": len(self.failed_deletions)
        }

        logger.info(f"Deletion complete. Deleted: {summary['deleted']}, Failed: {summary['failed']}")
        return summary

    def export_deleted_products(self, filename: str = None) -> str:
        """Export deleted product names to a text file."""
        if not filename:
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            filename = f"deleted_products_v2_{timestamp}.txt"

        try:
            with open(filename, 'w', encoding='utf-8') as f:
                f.write(f"Shopify Products Deleted from {SHOPIFY_STORE_URL}\n")
                f.write(f"Deletion Date: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\n")
                f.write(f"Total Products Deleted: {len(self.deleted_products)}\n")
                f.write("=" * 50 + "\n\n")

                for product in self.deleted_products:
                    status_indicator = " (already deleted)" if product.get('status') == 'already_deleted' else ""
                    f.write(f"{product['title']}{status_indicator}\n")

                if self.failed_deletions:
                    f.write("\n" + "=" * 50 + "\n")
                    f.write("FAILED DELETIONS:\n")
                    f.write("=" * 50 + "\n")
                    for product in self.failed_deletions:
                        f.write(f"{product['title']} (ID: {product['id']}) - {product['error']}\n")

            logger.info(f"Product list exported to: {filename}")
            return filename

        except Exception as e:
            logger.error(f"Failed to export product list: {e}")
            return ""


def main():
    """Main execution function."""
    logger.info("Starting Shopify Product Deleter v2.0")
    logger.info(f"Target store: {SHOPIFY_STORE_URL}")

    # Initialize deleter
    deleter = ShopifyProductDeleter(SHOPIFY_STORE_URL, ACCESS_TOKEN)

    try:
        # Test connection
        logger.info("Testing API connection...")
        test_response = deleter.make_request("GET", "shop.json")
        if not test_response:
            logger.error("Failed to connect to Shopify API. Please check your credentials.")
            sys.exit(1)

        shop_name = test_response.get("shop", {}).get("name", "Unknown")
        logger.info(f"Successfully connected to shop: {shop_name}")

        # Delete all products
        summary = deleter.delete_all_products()

        # Export results
        if summary["deleted"] > 0:
            export_file = deleter.export_deleted_products()
            print(f"\n🎉 Deletion Complete!")
            print(f"📊 Summary:")
            print(f"   ✅ Total products processed: {summary['total']}")
            print(f"   🗑️  Successfully deleted: {summary['deleted']}")
            print(f"   ❌ Failed deletions: {summary['failed']}")
            print(f"   📄 Product list exported to: {export_file}")

            if summary['failed'] > 0:
                print(f"\n⚠️  {summary['failed']} products failed to delete. Check the log file for details.")
        else:
            print("\n❌ No products were deleted.")

    except KeyboardInterrupt:
        logger.info("Operation cancelled by user")
        sys.exit(1)
    except Exception as e:
        logger.error(f"Unexpected error: {e}")
        sys.exit(1)


if __name__ == "__main__":
    main()
