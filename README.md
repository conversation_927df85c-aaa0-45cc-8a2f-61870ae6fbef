# Shopify Product Bulk Deleter

A production-ready Python script to delete all products from a Shopify store.

## Features

- ✅ Deletes all products from your Shopify store
- ✅ Counts deleted products
- ✅ Exports product names to a TXT file
- ✅ Production-ready with error handling and logging
- ✅ Rate limiting compliance
- ✅ Retry logic for failed requests
- ✅ User confirmation before deletion
- ✅ Detailed logging to file and console

## Prerequisites

- Python 3.7 or higher
- Shopify Admin API access token
- `requests` library

## Installation

1. Install dependencies:
```bash
pip install -r requirements.txt
```

## Usage

Run the script:
```bash
py shopify_product_deleter.py
```

The script will:
1. Connect to your Shopify store
2. Fetch all products
3. Ask for confirmation before deletion
4. Delete all products one by one
5. Generate a summary report
6. Export deleted product names to a TXT file

## Safety Features

- **Confirmation Required**: You must type 'DELETE ALL PRODUCTS' to confirm
- **Detailed Logging**: All operations are logged to a timestamped log file
- **Error Handling**: Failed deletions are tracked and reported
- **Rate Limiting**: Respects Shopify API rate limits

## Output Files

- `deleted_products_YYYYMMDD_HHMMSS.txt` - List of deleted product names
- `shopify_deletion_YYYYMMDD_HHMMSS.log` - Detailed operation log

## Configuration

The script is pre-configured for:
- Store: theprintgarage.myshopify.com
- Access Token: 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

## Warning

⚠️ **This script will permanently delete ALL products from your Shopify store. This action cannot be undone!**

Make sure you have a backup of your products before running this script.
